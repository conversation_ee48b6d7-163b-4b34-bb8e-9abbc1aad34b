# RTBS

**RewardTracker & BiliSearch** - 智能的奖励追踪与搜索工具

一个集成了Microsoft Rewards追踪和哔哩搜索功能的Chrome扩展，提供智能的数据检测和自动化搜索体验。

## 🎯 功能特点

### RewardTracker 模块
- **自动检测**: 智能识别必应奖励页面，自动检测用户账号和搜索进度
- **数据追踪**: 实时监控搜索天数进度，记录每日搜索状态
- **智能保存**: 自动保存到本地存储，创建文件到指定文件夹
- **状态指示**: 直观的绿色/红色指示器显示当前状态

### BiliSearch 模块  
- **智能搜索**: 自动生成随机搜索词，执行哔哩搜索任务
- **进度追踪**: 实时显示页面加载、搜索修改、搜索完成状态
- **自动化**: 完成搜索后自动关闭标签页
- **状态同步**: 与主面板同步显示搜索状态

### 智能面板显示
- **目标页面专注**: 在对应页面时只显示相关功能面板
- **统一设计**: 两个模块采用一致的设计语言和交互模式
- **动态切换**: 根据当前页面自动切换显示内容

## 📱 界面设计

### 双面板架构
```
┌─────────────────────────┐
│ RewardTracker       ●  │ ← 蓝色渐变标题
├─────────────────────────┤
│ 账号  <EMAIL>  │ ← 绿色渐变
├─────────────────────────┤
│ 天数  4/7              │ ← 橙粉色渐变
├─────────────────────────┤
│ 详情  连续搜索4/7天     │ ← 蓝紫色渐变
├─────────────────────────┤
│[刷新][查看][清除][目标] │ ← 功能按钮
├─────────────────────────┤
│ BiliSearch          ●  │ ← 粉色渐变标题
├─────────────────────────┤
│ 页面  已完成           │ ← 浅粉色渐变
├─────────────────────────┤
│ 修改  已修改           │ ← 浅紫色渐变
├─────────────────────────┤
│ 搜索  哔a哩3哔z哩       │ ← 浅橙色渐变
├─────────────────────────┤
│ 完成  已完成           │ ← 浅绿色渐变
└─────────────────────────┘
```

### 智能显示逻辑
- **必应奖励页面**: 只显示RewardTracker面板
- **哔哩搜索页面**: 只显示BiliSearch面板
- **其他页面**: 显示两个面板，数据显示为 --/--

## 🚀 安装使用

### 安装方法
1. 下载项目文件到本地
2. 打开Chrome浏览器
3. 访问 `chrome://extensions/`
4. 开启右上角的"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择项目文件夹

### 使用方法
1. **RewardTracker**: 访问必应奖励页面，扩展自动检测并保存数据
2. **BiliSearch**: 访问指定的哔哩搜索页面，扩展自动执行搜索任务
3. **查看状态**: 点击扩展图标查看当前状态和进度
4. **功能操作**: 使用弹窗中的按钮进行刷新、查看、清除等操作

## 📁 项目结构

```
RTBS/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 内容脚本
├── background.js          # 后台脚本
├── icon/                  # 图标文件夹
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # 说明文档
```

## 🎨 设计特色

### 视觉设计
- **渐变色彩**: 使用现代化的渐变背景设计
- **双主题**: RewardTracker蓝色主题，BiliSearch粉色主题
- **一致性**: 统一的布局、字体和交互模式
- **响应式**: 适配不同的显示状态和内容

### 用户体验
- **智能化**: 根据页面类型自动调整显示内容
- **专注性**: 在目标页面时只显示相关功能
- **直观性**: 清晰的状态指示和进度显示
- **流畅性**: 平滑的状态切换和数据更新

## ⚙️ 技术规格

- **框架**: Chrome Extension Manifest V3
- **存储**: Chrome Storage API
- **权限**: activeTab, storage, downloads, tabs
- **兼容**: Chrome 88+ 浏览器
- **架构**: 模块化设计，功能分离

## 📋 注意事项

1. **页面要求**: 需要在对应的目标页面使用相关功能
2. **权限设置**: 确保浏览器允许扩展访问页面内容
3. **数据安全**: 所有数据保存在本地，请定期备份重要信息
4. **网络环境**: 哔哩搜索功能需要稳定的网络连接

## 🔄 版本信息

### v1.0 (当前版本)
- 集成RewardTracker和BiliSearch双功能模块
- 实现智能面板显示和状态管理
- 采用现代化的渐变色彩设计
- 优化代码结构，移除调试信息
- 统一项目命名为RTBS

## 📄 许可证

本项目仅供学习和个人使用，请遵守相关网站的使用条款。

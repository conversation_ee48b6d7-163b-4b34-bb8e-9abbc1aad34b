// RTBS - 内容脚本

let hasDetectedData = false;
let detectionAttempts = 0;
const MAX_ATTEMPTS = 10;

// 检查是否是目标页面
function isTargetPage() {
  const url = window.location.href;
  return url.includes('rewards.bing.com') ||
         url.includes('bing.com/rewards') ||
         url.includes('www.bing.com/rewards');
}

// 检查是否是哔哩搜索页面
function isBiliSearchPage() {
  const url = window.location.href;
  return url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW') ||
         url.includes('cn.bing.com/search?q=哔哩哔哩') ||
         url.includes('cn.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9');
}

// 检查是否是哔哩搜索结果页面（搜索完成后的页面）
function isBiliSearchResultPage() {
  const url = window.location.href;
  // 检查是否是cn.bing.com的搜索页面，且包含哔哩相关的搜索词
  if (!url.includes('cn.bing.com/search')) {
    return false;
  }

  // 检查URL中是否包含哔哩相关的搜索词（编码或未编码）
  const hasOriginalBili = url.includes('%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9'); // 哔哩哔哩的编码
  const hasModifiedBili = /q=.*%E5%93%94.*%E5%93%A9.*%E5%93%94.*%E5%93%A9/.test(url); // 修改后的格式
  const hasDecodedBili = url.includes('哔') && url.includes('哩'); // 未编码的情况

  return hasOriginalBili || hasModifiedBili || hasDecodedBili;
}

// 检测用户账号
function detectUserAccount() {
  try {
    const accountElement = document.querySelector('#mectrl_currentAccount_secondary');
    return accountElement ? accountElement.textContent.trim() : null;
  } catch (error) {
    return null;
  }
}

// 检测搜索天数
function detectSearchDays() {
  try {
    const selector = '#more-activities > div > mee-card:nth-child(1) > div > card-content > mee-rewards-more-activities-card-item > div > a > div.contentContainer > p';
    const element = document.querySelector(selector);

    if (!element) return null;

    const text = element.textContent.trim();
    const dayMatch = text.match(/(\d+)\/(\d+)天/);

    if (dayMatch) {
      return {
        fullText: text,
        currentDays: parseInt(dayMatch[1]),
        totalDays: parseInt(dayMatch[2]),
        progress: `${dayMatch[1]}/${dayMatch[2]}`
      };
    }

    return null;
  } catch (error) {
    return null;
  }
}

// 执行检测并保存数据
function performDetection() {
  if (hasDetectedData || detectionAttempts >= MAX_ATTEMPTS) {
    return;
  }

  detectionAttempts++;

  const userAccount = detectUserAccount();
  const searchDays = detectSearchDays();

  if (userAccount && searchDays) {
    hasDetectedData = true;

    // 保存到本地存储
    const data = {
      userAccount: userAccount,
      searchDaysInfo: searchDays,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    // 存储数据
    chrome.storage.local.set({
      'rewardTracker_lastData': data,
      'rewardTracker_timestamp': Date.now()
    });

    // 只在目标页面才发送保存文件的消息
    if (isTargetPage()) {
      chrome.runtime.sendMessage({
        action: 'dataDetected',
        ...data
      });
    }
  }
}

// 主检测函数
function startDetection() {
  // 立即检测一次
  performDetection();

  // 如果还没检测到数据，继续尝试
  if (!hasDetectedData && detectionAttempts < MAX_ATTEMPTS) {
    setTimeout(() => {
      startDetection();
    }, 2000);
  }
}

// 生成随机字符（小写字母或数字）
function getRandomChar() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  return chars.charAt(Math.floor(Math.random() * chars.length));
}

// 哔哩搜索页面处理
function handleBiliSearchPage() {
  // 发送页面加载完毕状态
  chrome.storage.local.set({
    'biliSearch_status': {
      pageLoaded: true,
      searchModified: false,
      searchCompleted: false,
      timestamp: new Date().toISOString()
    }
  });

  // 等待页面完全加载
  setTimeout(() => {
    const searchInput = document.querySelector('#sb_form_q');
    const searchButton = document.querySelector('#sb_form_go');

    if (searchInput && searchButton) {
      // 生成随机搜索词：哔x哩x哔x哩
      const randomChar1 = getRandomChar();
      const randomChar2 = getRandomChar();
      const randomChar3 = getRandomChar();
      const newSearchTerm = `哔${randomChar1}哩${randomChar2}哔${randomChar3}哩`;

      // 修改搜索词
      searchInput.value = newSearchTerm;

      // 触发input事件，确保页面识别到值的变化
      const inputEvent = new Event('input', { bubbles: true });
      searchInput.dispatchEvent(inputEvent);

      // 更新状态
      chrome.storage.local.set({
        'biliSearch_status': {
          pageLoaded: true,
          searchModified: true,
          searchCompleted: false,
          searchTerm: newSearchTerm,
          timestamp: new Date().toISOString()
        }
      });

      // 触发搜索 - 多种方式确保成功
      // 方式1: 直接点击按钮
      searchButton.click();

      // 方式2: 触发表单提交
      const form = document.querySelector('#sb_form');
      if (form) {
        form.submit();
      }

      // 方式3: 模拟键盘回车
      const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        which: 13,
        bubbles: true
      });
      searchInput.dispatchEvent(enterEvent);

      // 方式4: 模拟鼠标点击事件
      const mouseEvent = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: 0,
        clientY: 0
      });
      searchButton.dispatchEvent(mouseEvent);

      // 方式5: 直接导航到搜索URL
      setTimeout(() => {
        const searchUrl = `https://cn.bing.com/search?q=${encodeURIComponent(newSearchTerm)}&qs=n&form=BILREW`;
        window.location.href = searchUrl;
      }, 500);

      // 不再在这里设置延时，因为页面会跳转，这些代码不会执行
    }
  }, 1000);
}

// 处理哔哩搜索结果页面
function handleBiliSearchResultPage() {
  // 从存储中获取当前状态，保持搜索词不变
  chrome.storage.local.get('biliSearch_status', (result) => {
    const currentStatus = result.biliSearch_status || {};

    // 更新状态为搜索完成，保持原有的搜索词
    chrome.storage.local.set({
      'biliSearch_status': {
        pageLoaded: true,
        searchModified: true,
        searchCompleted: true,
        searchTerm: currentStatus.searchTerm || '搜索已完成',
        timestamp: new Date().toISOString()
      }
    });
  });

  // 等待3秒后关闭标签页
  setTimeout(() => {
    chrome.runtime.sendMessage({
      action: 'closeBiliTab'
    });
  }, 3000);
}

// 页面加载完成后开始检测
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    if (isBiliSearchPage()) {
      handleBiliSearchPage();
    } else if (isBiliSearchResultPage()) {
      handleBiliSearchResultPage();
    } else {
      startDetection();
    }
  });
} else {
  if (isBiliSearchPage()) {
    handleBiliSearchPage();
  } else if (isBiliSearchResultPage()) {
    handleBiliSearchResultPage();
  } else {
    startDetection();
  }
}





